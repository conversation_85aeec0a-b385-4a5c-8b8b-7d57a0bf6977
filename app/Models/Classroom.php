<?php

declare(strict_types=1);

namespace App\Models;

use App\Observers\ClassroomObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $teacher_id Reference to the teacher owning the classroom
 */
#[ObservedBy([ClassroomObserver::class])]
class Classroom extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'description',
        'teacher_id',
        'capacity',
        'is_active',
        'room_number',
        'classroom_number',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    public function students(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Student::class);
    }

    public function schedules(): HasMany
    {
        return $this->hasMany(ClassroomSchedule::class);
    }

    public function attendanceRecords(): HasMany
    {
        return $this->hasMany(AttendanceSchedule::class);
    }

    /**
     * Get attendance records for a specific date
     */
    public function attendanceForDate($date): HasMany
    {
        return $this->attendanceRecords()->whereDate('date', $date);
    }

    /**
     * Get present students for a specific date
     */
    public function presentStudentsForDate($date)
    {
        return $this->attendanceRecords()
            ->whereDate('date', $date)
            ->where('type', 'Present')
            ->with('student');
    }

    /**
     * Get absent students for a specific date
     */
    public function absentStudentsForDate($date)
    {
        return $this->attendanceRecords()
            ->whereDate('date', $date)
            ->where('type', 'Absent')
            ->with('student');
    }

    /**
     * Get attendance statistics for a specific date
     */
    public function getAttendanceStatsForDate($date): array
    {
        $totalStudents = $this->students()->count();
        $presentCount = $this->presentStudentsForDate($date)->count();
        $absentCount = $this->absentStudentsForDate($date)->count();

        return [
            'total_students' => $totalStudents,
            'present_count' => $presentCount,
            'absent_count' => $absentCount,
            'attendance_percentage' => $totalStudents > 0 ? ($presentCount / $totalStudents) * 100 : 0,
        ];
    }

    /**
     * Get overall attendance totals for the classroom.
     */
    public function getAttendanceTotals(): array
    {
        $presentCount = $this->attendanceRecords()->present()->count();
        $absentCount = $this->attendanceRecords()->absent()->count();
        $total = $presentCount + $absentCount;

        return [
            'present_count' => $presentCount,
            'absent_count' => $absentCount,
            'attendance_percentage' => $total > 0 ? ($presentCount / $total) * 100 : 0,
        ];
    }

    /**
     * Get attendance trends for the last N days
     */
    public function getAttendanceTrends(int $days = 15): array
    {
        $trends = [];
        $startDate = now()->subDays($days - 1);

        for ($i = 0; $i < $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $stats = $this->getAttendanceStatsForDate($date->toDateString());

            $trends[] = [
                'date' => $date->toDateString(),
                'formatted_date' => $date->format('M d'),
                'attendance_percentage' => $stats['attendance_percentage'],
                'present_count' => $stats['present_count'],
                'total_students' => $stats['total_students'],
            ];
        }

        return $trends;
    }

    /**
     * Get performance metrics for the classroom
     */
    public function getPerformanceMetrics(): array
    {
        $totalStudents = $this->students()->count();
        $attendanceStats = $this->getAttendanceTotals();

        // Get exam performance
        $examStats = $this->getExamPerformanceStats();

        // Calculate overall performance score (weighted average)
        $attendanceWeight = 0.4;
        $examWeight = 0.6;

        $overallScore = ($attendanceStats['attendance_percentage'] * $attendanceWeight) +
                       ($examStats['average_score'] * $examWeight);

        return [
            'total_students' => $totalStudents,
            'attendance_percentage' => $attendanceStats['attendance_percentage'],
            'exam_average' => $examStats['average_score'],
            'overall_performance' => round($overallScore, 1),
            'grade_distribution' => $examStats['grade_distribution'],
            'improvement_trend' => $this->calculateImprovementTrend(),
        ];
    }

    /**
     * Get exam performance statistics
     */
    public function getExamPerformanceStats(): array
    {
        $exams = $this->hasMany(Exam::class)->with(['students.questions.ratings'])->get();

        if ($exams->isEmpty()) {
            return [
                'average_score' => 0,
                'total_exams' => 0,
                'grade_distribution' => [],
                'pass_rate' => 0,
            ];
        }

        $allScores = [];
        $gradeDistribution = ['A' => 0, 'B' => 0, 'C' => 0, 'D' => 0, 'F' => 0];

        foreach ($exams as $exam) {
            foreach ($exam->students as $examStudent) {
                $score = $examStudent->total_score ?? 0;
                $percentage = $this->calculateScorePercentage($score);
                $allScores[] = $percentage;

                // Calculate grade
                $grade = $this->getGradeFromPercentage($percentage);
                $gradeDistribution[$grade]++;
            }
        }

        $averageScore = count($allScores) > 0 ? array_sum($allScores) / count($allScores) : 0;
        $passRate = count($allScores) > 0 ? (count(array_filter($allScores, fn($score) => $score >= 60)) / count($allScores)) * 100 : 0;

        return [
            'average_score' => round($averageScore, 1),
            'total_exams' => $exams->count(),
            'grade_distribution' => $gradeDistribution,
            'pass_rate' => round($passRate, 1),
            'total_submissions' => count($allScores),
        ];
    }

    /**
     * Get top performing students
     */
    public function getTopStudents(int $limit = 5): array
    {
        $students = $this->students()->with(['attendanceRecords'])->get();
        $studentPerformance = [];

        foreach ($students as $student) {
            // Calculate attendance percentage
            $attendanceRecords = $student->attendanceRecords;
            $totalRecords = $attendanceRecords->count();
            $presentCount = $attendanceRecords->where('type', 'Present')->count();
            $attendancePercentage = $totalRecords > 0 ? ($presentCount / $totalRecords) * 100 : 0;

            // Get exam scores
            $examScores = $this->getStudentExamScores($student->id);
            $examAverage = count($examScores) > 0 ? array_sum($examScores) / count($examScores) : 0;

            // Calculate overall performance
            $overallPerformance = ($attendancePercentage * 0.4) + ($examAverage * 0.6);

            $studentPerformance[] = [
                'student' => $student,
                'attendance_percentage' => round($attendancePercentage, 1),
                'exam_average' => round($examAverage, 1),
                'overall_performance' => round($overallPerformance, 1),
                'total_exams' => count($examScores),
            ];
        }

        // Sort by overall performance and take top students
        usort($studentPerformance, fn($a, $b) => $b['overall_performance'] <=> $a['overall_performance']);

        return array_slice($studentPerformance, 0, $limit);
    }

    /**
     * Get latest exam results
     */
    public function getLatestExamResults(): ?array
    {
        $latestExam = $this->hasMany(Exam::class)
            ->with(['students.questions.ratings', 'students.student'])
            ->latest()
            ->first();

        if (!$latestExam) {
            return null;
        }

        $results = [];
        $allScores = [];

        foreach ($latestExam->students as $examStudent) {
            $score = $examStudent->total_score ?? 0;
            $percentage = $this->calculateScorePercentage($score);
            $allScores[] = $percentage;

            $results[] = [
                'student_name' => $examStudent->student->name,
                'score' => $score,
                'percentage' => round($percentage, 1),
                'grade' => $this->getGradeFromPercentage($percentage),
                'completed_at' => $examStudent->completed_at,
            ];
        }

        $averageScore = count($allScores) > 0 ? array_sum($allScores) / count($allScores) : 0;
        $passRate = count($allScores) > 0 ? (count(array_filter($allScores, fn($score) => $score >= 60)) / count($allScores)) * 100 : 0;

        return [
            'exam' => $latestExam,
            'results' => $results,
            'statistics' => [
                'average_score' => round($averageScore, 1),
                'pass_rate' => round($passRate, 1),
                'total_students' => count($results),
                'highest_score' => count($allScores) > 0 ? max($allScores) : 0,
                'lowest_score' => count($allScores) > 0 ? min($allScores) : 0,
            ],
        ];
    }

    /**
     * Helper method to calculate score percentage
     */
    private function calculateScorePercentage(float $score): float
    {
        // Assuming maximum possible score is 100, adjust as needed
        $maxScore = 100;
        return ($score / $maxScore) * 100;
    }

    /**
     * Helper method to get grade from percentage
     */
    private function getGradeFromPercentage(float $percentage): string
    {
        if ($percentage >= 90) return 'A';
        if ($percentage >= 80) return 'B';
        if ($percentage >= 70) return 'C';
        if ($percentage >= 60) return 'D';
        return 'F';
    }

    /**
     * Get student exam scores
     */
    private function getStudentExamScores(string $studentId): array
    {
        $examStudents = ExamStudent::where('student_id', $studentId)
            ->whereHas('exam', fn($query) => $query->where('classroom_id', $this->id))
            ->with('questions.ratings')
            ->get();

        $scores = [];
        foreach ($examStudents as $examStudent) {
            $score = $examStudent->total_score ?? 0;
            $scores[] = $this->calculateScorePercentage($score);
        }

        return $scores;
    }

    /**
     * Calculate improvement trend
     */
    private function calculateImprovementTrend(): string
    {
        // Get last 30 days attendance
        $recentAttendance = $this->getAttendanceTrends(30);

        if (count($recentAttendance) < 15) {
            return 'stable';
        }

        $firstHalf = array_slice($recentAttendance, 0, 15);
        $secondHalf = array_slice($recentAttendance, 15);

        $firstHalfAvg = array_sum(array_column($firstHalf, 'attendance_percentage')) / count($firstHalf);
        $secondHalfAvg = array_sum(array_column($secondHalf, 'attendance_percentage')) / count($secondHalf);

        $difference = $secondHalfAvg - $firstHalfAvg;

        if ($difference > 5) return 'improving';
        if ($difference < -5) return 'declining';
        return 'stable';
    }
}
