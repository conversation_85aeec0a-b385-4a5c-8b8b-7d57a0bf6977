<?php

declare(strict_types=1);

namespace App\Livewire\ClassroomDashboard;

use App\Models\Classroom;
use Livewire\Component;

class PerformanceOverview extends Component
{
    public Classroom $classroom;
    public array $performanceMetrics = [];
    public bool $isLoading = true;

    public function mount(Classroom $classroom): void
    {
        $this->classroom = $classroom;
        $this->loadPerformanceMetrics();
    }

    public function loadPerformanceMetrics(): void
    {
        $this->isLoading = true;
        
        try {
            $this->performanceMetrics = $this->classroom->getPerformanceMetrics();
        } catch (\Exception $e) {
            $this->performanceMetrics = [
                'total_students' => 0,
                'attendance_percentage' => 0,
                'exam_average' => 0,
                'overall_performance' => 0,
                'grade_distribution' => [],
                'improvement_trend' => 'stable',
            ];
        }
        
        $this->isLoading = false;
    }

    public function getPerformanceColor(): string
    {
        $score = $this->performanceMetrics['overall_performance'] ?? 0;
        
        if ($score >= 85) return 'text-green-600';
        if ($score >= 70) return 'text-yellow-600';
        return 'text-red-600';
    }

    public function getPerformanceIcon(): string
    {
        $score = $this->performanceMetrics['overall_performance'] ?? 0;
        
        if ($score >= 85) return 'heroicon-o-arrow-trending-up';
        if ($score >= 70) return 'heroicon-o-minus';
        return 'heroicon-o-arrow-trending-down';
    }

    public function getTrendIcon(): string
    {
        $trend = $this->performanceMetrics['improvement_trend'] ?? 'stable';
        
        return match ($trend) {
            'improving' => 'heroicon-o-arrow-trending-up',
            'declining' => 'heroicon-o-arrow-trending-down',
            default => 'heroicon-o-minus',
        };
    }

    public function getTrendColor(): string
    {
        $trend = $this->performanceMetrics['improvement_trend'] ?? 'stable';
        
        return match ($trend) {
            'improving' => 'text-green-600',
            'declining' => 'text-red-600',
            default => 'text-gray-600',
        };
    }

    public function render()
    {
        return view('livewire.classroom-dashboard.performance-overview');
    }
}
