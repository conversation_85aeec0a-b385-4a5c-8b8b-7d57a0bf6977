<?php

declare(strict_types=1);

namespace App\Livewire\ClassroomDashboard;

use App\Models\Classroom;
use App\Models\Exam;
use Exception;
use Livewire\Component;

class PerformanceOverview extends Component
{
    public Classroom $classroom;

    public array $performanceMetrics = [];

    public bool $isLoading = false;

    public function mount(Classroom $classroom): void
    {
        $this->classroom = $classroom;
        $this->loadPerformanceMetrics();
    }

    public function loadPerformanceMetrics(): void
    {
        $this->isLoading = true;

        try {
            $this->performanceMetrics = $this->calculatePerformanceMetrics();
        } catch (Exception $e) {
            $this->performanceMetrics = [
                'total_students' => 0,
                'attendance_percentage' => 0,
                'exam_average' => 0,
                'overall_performance' => 0,
                'grade_distribution' => [],
                'improvement_trend' => 'stable',
            ];
        }

        $this->isLoading = false;
    }

    public function getPerformanceColor(): string
    {
        $score = $this->performanceMetrics['overall_performance'] ?? 0;

        if ($score >= 85) {
            return 'text-green-600';
        }
        if ($score >= 70) {
            return 'text-yellow-600';
        }

        return 'text-red-600';
    }

    public function getPerformanceIcon(): string
    {
        $score = $this->performanceMetrics['overall_performance'] ?? 0;

        if ($score >= 85) {
            return 'heroicon-o-arrow-trending-up';
        }
        if ($score >= 70) {
            return 'heroicon-o-minus';
        }

        return 'heroicon-o-arrow-trending-down';
    }

    public function getTrendIcon(): string
    {
        $trend = $this->performanceMetrics['improvement_trend'] ?? 'stable';

        return match ($trend) {
            'improving' => 'heroicon-o-arrow-trending-up',
            'declining' => 'heroicon-o-arrow-trending-down',
            default => 'heroicon-o-minus',
        };
    }

    public function getTrendColor(): string
    {
        $trend = $this->performanceMetrics['improvement_trend'] ?? 'stable';

        return match ($trend) {
            'improving' => 'text-green-600',
            'declining' => 'text-red-600',
            default => 'text-gray-600',
        };
    }

    public function render()
    {
        return view('livewire.classroom-dashboard.performance-overview');
    }

    private function calculatePerformanceMetrics(): array
    {
        $totalStudents = $this->classroom->students()->count();
        $attendanceStats = $this->getAttendanceTotals();

        // Get exam performance
        $examStats = $this->getExamPerformanceStats();

        // Calculate overall performance score (weighted average)
        $attendanceWeight = 0.4;
        $examWeight = 0.6;

        $overallScore = ($attendanceStats['attendance_percentage'] * $attendanceWeight) +
                       ($examStats['average_score'] * $examWeight);

        return [
            'total_students' => $totalStudents,
            'attendance_percentage' => $attendanceStats['attendance_percentage'],
            'exam_average' => $examStats['average_score'],
            'overall_performance' => round($overallScore, 1),
            'grade_distribution' => $examStats['grade_distribution'],
            'improvement_trend' => $this->calculateImprovementTrend(),
        ];
    }

    private function getAttendanceTotals(): array
    {
        $presentCount = $this->classroom->attendanceRecords()->present()->count();
        $absentCount = $this->classroom->attendanceRecords()->absent()->count();
        $total = $presentCount + $absentCount;

//        dd($presentCount, $absentCount, $total);

        return [
            'present_count' => $presentCount,
            'absent_count' => $absentCount,
            'total' => $total,
            'attendance_percentage' => $total > 0 ? ($presentCount / $total) * 100 : 0,
        ];
    }

    private function getExamPerformanceStats(): array
    {
        $exams = Exam::where('classroom_id', $this->classroom->id)
            ->with(['students.questions.ratings'])
            ->get();

        if ($exams->isEmpty()) {
            return [
                'average_score' => 0,
                'total_exams' => 0,
                'grade_distribution' => ['A' => 0, 'B' => 0, 'C' => 0, 'D' => 0, 'F' => 0],
                'pass_rate' => 0,
            ];
        }

        $allScores = [];
        $gradeDistribution = ['A' => 0, 'B' => 0, 'C' => 0, 'D' => 0, 'F' => 0];

        foreach ($exams as $exam) {
            foreach ($exam->students as $examStudent) {
                $score = $examStudent->total_score ?? 0;
                $percentage = $this->calculateScorePercentage($score, $examStudent->questions->count());
                $allScores[] = $percentage;

                // Calculate grade
                $grade = $this->getGradeFromPercentage($percentage);
                $gradeDistribution[$grade]++;
            }
        }

        $averageScore = count($allScores) > 0 ? array_sum($allScores) / count($allScores) : 0;
        $passRate = count($allScores) > 0 ? (count(array_filter($allScores, fn ($score) => $score >= 60)) / count($allScores)) * 100 : 0;

        return [
            'average_score' => round($averageScore, 1),
            'total_exams' => $exams->count(),
            'grade_distribution' => $gradeDistribution,
            'pass_rate' => round($passRate, 1),
            'total_submissions' => count($allScores),
        ];
    }

    private function calculateScorePercentage(float $score, int $totalQuestions): float
    {

        return $score / $totalQuestions;
    }

    private function getGradeFromPercentage(float $percentage): string
    {
        if ($percentage >= 90) {
            return 'A';
        }
        if ($percentage >= 80) {
            return 'B';
        }
        if ($percentage >= 70) {
            return 'C';
        }
        if ($percentage >= 60) {
            return 'D';
        }

        return 'F';
    }

    private function calculateImprovementTrend(): string
    {
        // Get last 30 days attendance
        $recentAttendance = $this->getAttendanceTrends(30);

        if (count($recentAttendance) < 15) {
            return 'stable';
        }

        $firstHalf = array_slice($recentAttendance, 0, 15);
        $secondHalf = array_slice($recentAttendance, 15);

        $firstHalfAvg = array_sum(array_column($firstHalf, 'attendance_percentage')) / count($firstHalf);
        $secondHalfAvg = array_sum(array_column($secondHalf, 'attendance_percentage')) / count($secondHalf);

        $difference = $secondHalfAvg - $firstHalfAvg;

        if ($difference > 5) {
            return 'improving';
        }
        if ($difference < -5) {
            return 'declining';
        }

        return 'stable';
    }

    private function getAttendanceTrends(int $days = 15): array
    {
        $trends = [];
        $startDate = now()->subDays($days - 1);

        for ($i = 0; $i < $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $stats = $this->classroom->getAttendanceStatsForDate($date->toDateString());

            $trends[] = [
                'date' => $date->toDateString(),
                'formatted_date' => $date->format('M d'),
                'attendance_percentage' => $stats['attendance_percentage'],
                'present_count' => $stats['present_count'],
                'total_students' => $stats['total_students'],
            ];
        }

        return $trends;
    }
}
