<?php

declare(strict_types=1);

namespace App\Livewire\ClassroomDashboard;

use App\Models\Classroom;
use Livewire\Component;

class LatestExamResults extends Component
{
    public Classroom $classroom;
    public ?array $examResults = null;
    public bool $isLoading = true;

    public function mount(Classroom $classroom): void
    {
        $this->classroom = $classroom;
        $this->loadLatestExamResults();
    }

    public function loadLatestExamResults(): void
    {
        $this->isLoading = true;
        
        try {
            $this->examResults = $this->classroom->getLatestExamResults();
        } catch (\Exception $e) {
            $this->examResults = null;
        }
        
        $this->isLoading = false;
    }

    public function getGradeColor(string $grade): string
    {
        return match ($grade) {
            'A' => 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-300',
            'B' => 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300',
            'C' => 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-300',
            'D' => 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-300',
            'F' => 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-300',
            default => 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-300',
        };
    }

    public function getScoreColor(float $percentage): string
    {
        if ($percentage >= 85) return 'text-green-600';
        if ($percentage >= 70) return 'text-yellow-600';
        return 'text-red-600';
    }

    public function getPassRateColor(float $passRate): string
    {
        if ($passRate >= 80) return 'text-green-600';
        if ($passRate >= 60) return 'text-yellow-600';
        return 'text-red-600';
    }

    public function formatDate(?string $date): string
    {
        if (!$date) return __('Not completed');
        
        return \Carbon\Carbon::parse($date)->format('M d, Y');
    }

    public function render()
    {
        return view('livewire.classroom-dashboard.latest-exam-results');
    }
}
