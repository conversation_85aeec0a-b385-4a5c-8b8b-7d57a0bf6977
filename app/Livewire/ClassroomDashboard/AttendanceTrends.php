<?php

declare(strict_types=1);

namespace App\Livewire\ClassroomDashboard;

use App\Models\Classroom;
use Livewire\Component;

class AttendanceTrends extends Component
{
    public Classroom $classroom;
    public array $attendanceTrends = [];
    public bool $isLoading = true;
    public int $days = 15;

    public function mount(Classroom $classroom, int $days = 15): void
    {
        $this->classroom = $classroom;
        $this->days = $days;
        $this->loadAttendanceTrends();
    }

    public function loadAttendanceTrends(): void
    {
        $this->isLoading = true;

        try {
            $this->attendanceTrends = $this->classroom->getAttendanceTrends($this->days);
        } catch (\Exception $e) {
            $this->attendanceTrends = [];
        }

        $this->isLoading = false;
    }

    public function getAverageAttendance(): float
    {
        if (empty($this->attendanceTrends)) {
            return 0;
        }

        $total = array_sum(array_column($this->attendanceTrends, 'attendance_percentage'));
        return $total / count($this->attendanceTrends);
    }

    public function getTrendDirection(): string
    {
        if (count($this->attendanceTrends) < 2) {
            return 'stable';
        }

        $half = (int) ceil(count($this->attendanceTrends) / 2);

        $firstHalf = array_slice($this->attendanceTrends, 0, $half);
        $secondHalf = array_slice($this->attendanceTrends, $half);


        $firstAvg = array_sum(array_column($firstHalf, 'attendance_percentage')) / count($firstHalf);
        $secondAvg = array_sum(array_column($secondHalf, 'attendance_percentage')) / count($secondHalf);

        $difference = $secondAvg - $firstAvg;

        if ($difference > 5) return 'improving';
        if ($difference < -5) return 'declining';
        return 'stable';
    }

    public function getTrendIcon(): string
    {
        return match ($this->getTrendDirection()) {
            'improving' => 'heroicon-o-arrow-trending-up',
            'declining' => 'heroicon-o-arrow-trending-down',
            default => 'heroicon-o-minus',
        };
    }

    public function getTrendColor(): string
    {
        return match ($this->getTrendDirection()) {
            'improving' => 'text-green-600',
            'declining' => 'text-red-600',
            default => 'text-gray-600',
        };
    }

    public function getAttendanceColor(float $percentage): string
    {
        if ($percentage >= 85) return 'bg-green-500';
        if ($percentage >= 70) return 'bg-yellow-500';
        return 'bg-red-500';
    }

    public function getMaxHeight(): float
    {
        if (empty($this->attendanceTrends)) {
            return 100;
        }

        return max(array_column($this->attendanceTrends, 'attendance_percentage')) ?: 100;
    }

    public function render()
    {
        return view('livewire.classroom-dashboard.attendance-trends');
    }
}
