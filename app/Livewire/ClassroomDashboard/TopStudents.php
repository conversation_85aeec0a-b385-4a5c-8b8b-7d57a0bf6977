<?php

declare(strict_types=1);

namespace App\Livewire\ClassroomDashboard;

use App\Models\Classroom;
use Livewire\Component;

class TopStudents extends Component
{
    public Classroom $classroom;
    public array $topStudents = [];
    public bool $isLoading = true;
    public int $limit = 5;

    public function mount(Classroom $classroom, int $limit = 5): void
    {
        $this->classroom = $classroom;
        $this->limit = $limit;
        $this->loadTopStudents();
    }

    public function loadTopStudents(): void
    {
        $this->isLoading = true;
        
        try {
            $this->topStudents = $this->classroom->getTopStudents($this->limit);
        } catch (\Exception $e) {
            $this->topStudents = [];
        }
        
        $this->isLoading = false;
    }

    public function getPerformanceColor(float $performance): string
    {
        if ($performance >= 85) return 'text-green-600';
        if ($performance >= 70) return 'text-yellow-600';
        return 'text-red-600';
    }

    public function getPerformanceBadgeColor(float $performance): string
    {
        if ($performance >= 85) return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
        if ($performance >= 70) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
    }

    public function getRankIcon(int $rank): string
    {
        return match ($rank) {
            1 => '🥇',
            2 => '🥈',
            3 => '🥉',
            default => '#' . $rank,
        };
    }

    public function getRankColor(int $rank): string
    {
        return match ($rank) {
            1 => 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20',
            2 => 'text-gray-600 bg-gray-50 dark:bg-gray-900/20',
            3 => 'text-orange-600 bg-orange-50 dark:bg-orange-900/20',
            default => 'text-blue-600 bg-blue-50 dark:bg-blue-900/20',
        };
    }

    public function render()
    {
        return view('livewire.classroom-dashboard.top-students');
    }
}
