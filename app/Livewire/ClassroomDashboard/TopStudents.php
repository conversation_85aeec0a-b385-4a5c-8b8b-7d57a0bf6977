<?php

declare(strict_types=1);

namespace App\Livewire\ClassroomDashboard;

use App\Models\Classroom;
use App\Models\ExamStudent;
use Exception;
use Livewire\Component;

class TopStudents extends Component
{
    public Classroom $classroom;

    public array $topStudents = [];

    public bool $isLoading = true;

    public int $limit = 5;

    public function mount(Classroom $classroom, int $limit = 5): void
    {
        $this->classroom = $classroom;
        $this->limit = $limit;
        $this->loadTopStudents();
    }

    public function loadTopStudents(): void
    {
        $this->isLoading = true;

        try {
            $this->topStudents = $this->calculateTopStudents($this->limit);
        } catch (Exception $e) {
            $this->topStudents = [];
        }

        $this->isLoading = false;
    }

    public function getPerformanceColor(float $performance): string
    {
        if ($performance >= 85) {
            return 'text-green-600';
        }
        if ($performance >= 70) {
            return 'text-yellow-600';
        }

        return 'text-red-600';
    }

    public function getPerformanceBadgeColor(float $performance): string
    {
        if ($performance >= 85) {
            return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
        }
        if ($performance >= 70) {
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
        }

        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
    }

    public function getRankIcon(int $rank): string
    {
        return match ($rank) {
            1 => '🥇',
            2 => '🥈',
            3 => '🥉',
            default => '#'.$rank,
        };
    }

    public function getRankColor(int $rank): string
    {
        return match ($rank) {
            1 => 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20',
            2 => 'text-gray-600 bg-gray-50 dark:bg-gray-900/20',
            3 => 'text-orange-600 bg-orange-50 dark:bg-orange-900/20',
            default => 'text-blue-600 bg-blue-50 dark:bg-blue-900/20',
        };
    }

    public function render()
    {
        return view('livewire.classroom-dashboard.top-students');
    }

    private function calculateTopStudents(int $limit = 5): array
    {
        $students = $this->classroom->students()->with(['attendanceRecords'])->get();
        $studentPerformance = [];

        foreach ($students as $student) {
            // Calculate attendance percentage
            $attendanceRecords = $student->attendanceRecords;
            $totalRecords = $attendanceRecords->count();
            $presentCount = $attendanceRecords->where('type', 'Present')->count();
            $attendancePercentage = $totalRecords > 0 ? ($presentCount / $totalRecords) * 100 : 0;

            // Get exam scores
            $examScores = $this->getStudentExamScores($student->id);
            $examAverage = count($examScores) > 0 ? array_sum($examScores) / count($examScores) : 0;

            // Calculate overall performance
            $overallPerformance = ($attendancePercentage * 0.4) + ($examAverage * 0.6);

            $studentPerformance[] = [
                'student' => $student,
                'attendance_percentage' => round($attendancePercentage, 1),
                'exam_average' => round($examAverage, 1),
                'overall_performance' => round($overallPerformance, 1),
                'total_exams' => count($examScores),
            ];
        }

        // Sort by overall performance and take top students
        usort($studentPerformance, fn ($a, $b) => $b['overall_performance'] <=> $a['overall_performance']);

        return array_slice($studentPerformance, 0, $limit);
    }

    private function getStudentExamScores(string $studentId): array
    {
        $examStudents = ExamStudent::where('student_id', $studentId)
            ->whereHas('exam', fn ($query) => $query->where('classroom_id', $this->classroom->id))
            ->with('questions.ratings')
            ->get();

        $scores = [];
        foreach ($examStudents as $examStudent) {
            $score = $examStudent->total_score ?? 0;
            $scores[] = $this->calculateScorePercentage($score);
        }

        return $scores;
    }

    private function calculateScorePercentage(float $score): float
    {
        // Assuming maximum possible score is 100, adjust as needed
        $maxScore = 100;

        return ($score / $maxScore) * 100;
    }
}
