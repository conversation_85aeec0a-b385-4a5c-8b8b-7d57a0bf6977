<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\TeacherResource\Pages\CreateTeacher;
use App\Filament\Resources\TeacherResource\Pages\EditTeacher;
use App\Filament\Resources\TeacherResource\Pages\ListTeachers;
use App\Models\Teacher;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use UnitEnum;

class TeacherResource extends Resource
{
    protected static ?string $model = Teacher::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-user-circle';

    protected static string|UnitEnum|null $navigationGroup = 'School Management';

    protected static ?int $navigationSort = 3;

    public static function getPluralModelLabel(): string
    {
        return __('Teachers');
    }

    public static function getLabel(): string
    {
        return __('Teacher');
    }

    public static function getNavigationGroup(): ?string
    {
        return __(static::$navigationGroup);
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Section::make(__('Teacher Information'))
                    ->schema([
                        TextInput::make('name')
                            ->label('Full Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255),

                        TextInput::make('email')
                            ->label('Email Address')
                            ->translateLabel()
                            ->email()
                            ->maxLength(255),

                        TextInput::make('phone')
                            ->label('Phone Number')
                            ->translateLabel()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),

                        Textarea::make('address')
                            ->label('Address')
                            ->translateLabel()
                            ->rows(3),

                        TextInput::make('national_id')
                            ->label('National ID')
                            ->translateLabel()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),

                        TextInput::make('emergency_contact')
                            ->label('Emergency Contact')
                            ->translateLabel()
                            ->maxLength(255),

                        Textarea::make('notes')
                            ->label('Notes')
                            ->translateLabel()
                            ->rows(3),
                        Select::make('classrooms')
                            ->label('Classrooms')
                            ->translateLabel()
                            ->relationship('classrooms', 'name')
                            ->multiple()
                            ->preload()
                            ->disabled()
                            ->searchable(),
                    ])
                    ->columns(2),

                Section::make(__('Password'))
                    ->schema([
                        TextInput::make('password')
                            ->label('Password')
                            ->translateLabel()
                            ->password()
                            ->revealable()
                            ->dehydrateStateUsing(fn ($state) => ! empty($state) ? bcrypt($state) : null)
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $context): bool => $context === 'create'),

                        TextInput::make('password_confirmation')
                            ->label('Confirm Password')
                            ->translateLabel()
                            ->password()
                            ->revealable()
                            ->required(fn (string $context, callable $get): bool => $context === 'create' || filled($get('password'))
                            )
                            ->same('password'),
                    ])
                    ->columns(2),
            ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Full Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('email')
                    ->label('Email Address')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('phone')
                    ->label('Phone Number')
                    ->translateLabel()
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListTeachers::route('/'),
            'create' => CreateTeacher::route('/create'),
            'edit' => EditTeacher::route('/{record}/edit'),
        ];
    }
}
