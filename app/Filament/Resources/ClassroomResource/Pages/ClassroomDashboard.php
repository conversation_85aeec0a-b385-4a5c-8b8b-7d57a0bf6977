<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassroomResource\Pages;

use App\Filament\Resources\ClassroomResource;
use BackedEnum;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class ClassroomDashboard extends ViewRecord
{
    protected static string $resource = ClassroomResource::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-chart-pie';

    protected string $view = 'filament.resources.classroom-resource.pages.classroom-dashboard';

    public static function getNavigationLabel(): string
    {
        return __('Dashboard');
    }

    public function getTitle(): string|Htmlable
    {
        return __('Classroom Dashboard');
    }
}
