<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassroomResource\Pages;

use App\Filament\Resources\ClassroomResource;
use App\Models\Classroom;
use BackedEnum;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class ClassroomDashboard extends ViewRecord
{
    protected static string $resource = ClassroomResource::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-chart-pie';

    protected string $view = 'filament.resources.classroom-resource.pages.classroom-dashboard';

    public static function getNavigationLabel(): string
    {
        return __('Dashboard');
    }

    public function getTitle(): string|Htmlable
    {
        return __('Classroom Dashboard');
    }

    public function getHeading(): string|Htmlable
    {
        return $this->getRecord()->name . ' - ' . __('Dashboard');
    }

    public function getSubheading(): string|Htmlable|null
    {
        $classroom = $this->getRecord();
        $studentCount = $classroom->students()->count();
        $teacherName = $classroom->teacher->name ?? __('No teacher assigned');
        
        return __(':count students • Teacher: :teacher', [
            'count' => $studentCount,
            'teacher' => $teacherName,
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('manageStudents')
                ->label(__('Manage Students'))
                ->icon('heroicon-o-user-group')
                ->url(fn () => ClassroomResource::getUrl('students', ['record' => $this->getRecord()])),
                
            \Filament\Actions\Action::make('markAttendance')
                ->label(__('Mark Attendance'))
                ->icon('heroicon-o-calendar-days')
                ->url(fn () => ClassroomResource::getUrl('attendance', ['record' => $this->getRecord()])),
                
            \Filament\Actions\Action::make('viewReports')
                ->label(__('View Reports'))
                ->icon('heroicon-o-chart-bar')
                ->url(fn () => ClassroomResource::getUrl('report', ['record' => $this->getRecord()])),
        ];
    }
}
