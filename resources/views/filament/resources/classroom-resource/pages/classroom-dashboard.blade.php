<x-filament-panels::page>
    <!-- Header Section -->
    <div class="mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <!-- Classroom Info -->
                <div class="flex-1">
                    <div class="flex items-center space-x-4 mb-4 lg:mb-0">
                        <!-- Classroom Icon -->
                        <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                            <x-filament::icon icon="heroicon-o-academic-cap" class="w-8 h-8 text-white" />
                        </div>
                        
                        <!-- Basic Info -->
                        <div class="flex-1">
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ $record->name }}
                            </h1>
                            <div class="flex flex-wrap items-center gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                                @if($record->classroom_number)
                                    <div class="flex items-center space-x-1">
                                        <x-filament::icon icon="heroicon-o-hashtag" class="w-4 h-4" />
                                        <span>{{ __('Room') }} {{ $record->classroom_number }}</span>
                                    </div>
                                @endif
                                
                                @if($record->room_number)
                                    <div class="flex items-center space-x-1">
                                        <x-filament::icon icon="heroicon-o-map-pin" class="w-4 h-4" />
                                        <span>{{ __('Location') }} {{ $record->room_number }}</span>
                                    </div>
                                @endif
                                
                                <div class="flex items-center space-x-1">
                                    <x-filament::icon icon="heroicon-o-user" class="w-4 h-4" />
                                    <span>{{ $record->teacher->name ?? __('No teacher assigned') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    @if($record->description)
                        <p class="text-gray-600 dark:text-gray-400 mt-4">
                            {{ $record->description }}
                        </p>
                    @endif
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 lg:grid-cols-1 gap-4 lg:gap-2 mt-6 lg:mt-0 lg:ml-8">
                    <div class="text-center lg:text-right">
                        <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                            {{ $record->students()->count() }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Students') }}
                        </div>
                    </div>
                    <div class="text-center lg:text-right">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ $record->capacity }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Capacity') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Badge -->
            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        @if($record->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                                <x-filament::icon icon="heroicon-o-check-circle" class="w-3 h-3 mr-1" />
                                {{ __('Active') }}
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300">
                                <x-filament::icon icon="heroicon-o-x-circle" class="w-3 h-3 mr-1" />
                                {{ __('Inactive') }}
                            </span>
                        @endif
                        
                        @php
                            $utilizationPercentage = $record->capacity > 0 ? ($record->students()->count() / $record->capacity) * 100 : 0;
                        @endphp
                        
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            {{ number_format($utilizationPercentage, 1) }}% {{ __('utilized') }}
                        </span>
                    </div>
                    
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Last updated') }}: {{ $record->updated_at->diffForHumans() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Cards Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Performance Overview Card -->
        <div class="lg:col-span-1">
            @livewire('classroom-dashboard.performance-overview', ['classroom' => $record])
        </div>

        <!-- Top Students Card -->
        <div class="lg:col-span-1">
            @livewire('classroom-dashboard.top-students', ['classroom' => $record])
        </div>

        <!-- Latest Exam Results Card -->
        <div class="lg:col-span-1">
            @livewire('classroom-dashboard.latest-exam-results', ['classroom' => $record])
        </div>

        <!-- Attendance Trends Card -->
        <div class="lg:col-span-1">
            @livewire('classroom-dashboard.attendance-trends', ['classroom' => $record])
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="mt-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {{ __('Quick Actions') }}
            </h3>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a 
                    href="{{ \App\Filament\Resources\ClassroomResource::getUrl('students', ['record' => $record]) }}"
                    class="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                >
                    <x-filament::icon icon="heroicon-o-user-group" class="w-8 h-8 text-blue-600 mr-3" />
                    <div>
                        <div class="font-medium text-blue-900 dark:text-blue-100">
                            {{ __('Manage Students') }}
                        </div>
                        <div class="text-sm text-blue-600 dark:text-blue-300">
                            {{ __('Add, edit, or remove students') }}
                        </div>
                    </div>
                </a>

                <a 
                    href="{{ \App\Filament\Resources\ClassroomResource::getUrl('attendance', ['record' => $record]) }}"
                    class="flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                >
                    <x-filament::icon icon="heroicon-o-calendar-days" class="w-8 h-8 text-green-600 mr-3" />
                    <div>
                        <div class="font-medium text-green-900 dark:text-green-100">
                            {{ __('Mark Attendance') }}
                        </div>
                        <div class="text-sm text-green-600 dark:text-green-300">
                            {{ __('Record daily attendance') }}
                        </div>
                    </div>
                </a>

                <a 
                    href="{{ \App\Filament\Resources\ClassroomResource::getUrl('schedules', ['record' => $record]) }}"
                    class="flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
                >
                    <x-filament::icon icon="heroicon-o-clock" class="w-8 h-8 text-purple-600 mr-3" />
                    <div>
                        <div class="font-medium text-purple-900 dark:text-purple-100">
                            {{ __('Manage Schedule') }}
                        </div>
                        <div class="text-sm text-purple-600 dark:text-purple-300">
                            {{ __('Set class times and days') }}
                        </div>
                    </div>
                </a>

                <a 
                    href="{{ \App\Filament\Resources\ClassroomResource::getUrl('report', ['record' => $record]) }}"
                    class="flex items-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors"
                >
                    <x-filament::icon icon="heroicon-o-chart-bar" class="w-8 h-8 text-orange-600 mr-3" />
                    <div>
                        <div class="font-medium text-orange-900 dark:text-orange-100">
                            {{ __('View Reports') }}
                        </div>
                        <div class="text-sm text-orange-600 dark:text-orange-300">
                            {{ __('Detailed analytics and reports') }}
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</x-filament-panels::page>
