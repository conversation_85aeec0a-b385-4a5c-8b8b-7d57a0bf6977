<x-filament-panels::page>
    <!-- Header Section -->
    <div class="mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <!-- Classroom Info -->
                <div class="flex-1">
                    <div class="flex items-center space-x-4 mb-4 lg:mb-0">
                        <!-- Classroom Icon -->
                        <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                            <x-filament::icon icon="heroicon-o-academic-cap" class="w-8 h-8 text-white" />
                        </div>

                        <!-- Basic Info -->
                        <div class="flex-1">
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ $record->name }}
                            </h1>
                            <div class="flex flex-wrap items-center gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                                @if($record->classroom_number)
                                    <div class="flex items-center space-x-1">
                                        <x-filament::icon icon="heroicon-o-hashtag" class="w-4 h-4" />
                                        <span>{{ __('Room') }} {{ $record->classroom_number }}</span>
                                    </div>
                                @endif

                                @if($record->room_number)
                                    <div class="flex items-center space-x-1">
                                        <x-filament::icon icon="heroicon-o-map-pin" class="w-4 h-4" />
                                        <span>{{ __('Location') }} {{ $record->room_number }}</span>
                                    </div>
                                @endif

                                <div class="flex items-center space-x-1">
                                    <x-filament::icon icon="heroicon-o-user" class="w-4 h-4" />
                                    <span>{{ $record->teacher->name ?? __('No teacher assigned') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($record->description)
                        <p class="text-gray-600 dark:text-gray-400 mt-4">
                            {{ $record->description }}
                        </p>
                    @endif
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 lg:grid-cols-1 gap-4 lg:gap-2 mt-6 lg:mt-0 lg:ml-8">
                    <div class="text-center lg:text-right">
                        <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                            {{ $record->students()->count() }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Students') }}
                        </div>
                    </div>
                    <div class="text-center lg:text-right">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ $record->capacity }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Capacity') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Badge -->
            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        @if($record->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                                <x-filament::icon icon="heroicon-o-check-circle" class="w-3 h-3 mr-1" />
                                {{ __('Active') }}
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300">
                                <x-filament::icon icon="heroicon-o-x-circle" class="w-3 h-3 mr-1" />
                                {{ __('Inactive') }}
                            </span>
                        @endif

                        @php
                            $utilizationPercentage = $record->capacity > 0 ? ($record->students()->count() / $record->capacity) * 100 : 0;
                        @endphp

                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            {{ number_format($utilizationPercentage, 1) }}% {{ __('utilized') }}
                        </span>
                    </div>

                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Last updated') }}: {{ $record->updated_at->diffForHumans() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Cards Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Performance Overview Card -->
        <div class="lg:col-span-1">
            @livewire('classroom-dashboard.performance-overview', ['classroom' => $record])
        </div>

        <!-- Top Students Card -->
        <div class="lg:col-span-1">
            @livewire('classroom-dashboard.top-students', ['classroom' => $record])
        </div>

        <!-- Latest Exam Results Card -->
        <div class="lg:col-span-1">
            @livewire('classroom-dashboard.latest-exam-results', ['classroom' => $record])
        </div>

        <!-- Attendance Trends Card -->
        <div class="lg:col-span-1">
            @livewire('classroom-dashboard.attendance-trends', ['classroom' => $record])
        </div>
    </div>


</x-filament-panels::page>
