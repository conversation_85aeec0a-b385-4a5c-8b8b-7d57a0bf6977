<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ __('Performance Overview') }}
        </h3>
        @if($isLoading)
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div>
        @else
            <x-filament::icon
                :icon="$this->getPerformanceIcon()"
                class="w-5 h-5 {{ $this->getPerformanceColor() }}"
            />
        @endif
    </div>

    @if($isLoading)
        <div class="space-y-4">
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-2"></div>
                <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            </div>
        </div>
    @else
        <div class="space-y-6">
            <!-- Overall Performance Score -->
            <div class="text-center">
                <div class="relative inline-flex items-center justify-center w-32 h-32">
                    <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                            class="text-gray-200 dark:text-gray-700"
                            stroke="currentColor"
                            stroke-width="2"
                            fill="none"
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        />
                        <path
                            class="{{ $this->getPerformanceColor() }}"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            fill="none"
                            stroke-dasharray="{{ $performanceMetrics['overall_performance'] ?? 0 }}, 100"
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        />
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-2xl font-bold {{ $this->getPerformanceColor() }}">
                                {{ number_format($performanceMetrics['overall_performance'] ?? 0, 1) }}%
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                {{ __('Overall') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Metrics Grid -->
            <div class="grid grid-cols-2 gap-4">
                <!-- Attendance -->
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">
                            {{ __('Attendance') }}
                        </span>
                        <x-filament::icon icon="heroicon-o-calendar-days" class="w-4 h-4 text-blue-600" />
                    </div>
                    <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                        {{ number_format($performanceMetrics['attendance_percentage'] ?? 0, 1) }}%
                    </div>
                    <div class="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2 mt-2">
                        <div
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style="width: {{ $performanceMetrics['attendance_percentage'] ?? 0 }}%"
                        ></div>
                    </div>
                </div>

                <!-- Exam Average -->
                <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-green-700 dark:text-green-300">
                            {{ __('Exam Average') }}
                        </span>
                        <x-filament::icon icon="heroicon-o-academic-cap" class="w-4 h-4 text-green-600" />
                    </div>
                    <div class="text-2xl font-bold text-green-900 dark:text-green-100">
                        {{ number_format($performanceMetrics['exam_average'] ?? 0, 1) }}%
                    </div>
                    <div class="w-full bg-green-200 dark:bg-green-800 rounded-full h-2 mt-2">
                        <div
                            class="bg-green-600 h-2 rounded-full transition-all duration-300"
                            style="width: {{ $performanceMetrics['exam_average'] ?? 0 }}%"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Improvement Trend -->
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-center space-x-3">
                    <x-filament::icon
                        :icon="$this->getTrendIcon()"
                        class="w-5 h-5 {{ $this->getTrendColor() }}"
                    />
                    <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ __('Trend') }}
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            {{ __('Last 30 days') }}
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm font-semibold {{ $this->getTrendColor() }}">
                        {{ ucfirst(__($performanceMetrics['improvement_trend'] ?? 'stable')) }}
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        {{ $performanceMetrics['total_students'] ?? 0 }} {{ __('students') }}
                    </div>
                </div>
            </div>

            <!-- Grade Distribution -->
            @if(!empty($performanceMetrics['grade_distribution']))
                <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
                        {{ __('Grade Distribution') }}
                    </h4>
                    <div class="space-y-2">
                        @foreach($performanceMetrics['grade_distribution'] as $grade => $count)
                            @php
                                $total = array_sum($performanceMetrics['grade_distribution']);
                                $percentage = $total > 0 ? ($count / $total) * 100 : 0;
                                $gradeColors = [
                                    'A' => 'bg-green-500',
                                    'B' => 'bg-blue-500',
                                    'C' => 'bg-yellow-500',
                                    'D' => 'bg-orange-500',
                                    'F' => 'bg-red-500',
                                ];
                            @endphp
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 rounded {{ $gradeColors[$grade] ?? 'bg-gray-500' }}"></div>
                                    <span class="text-sm text-gray-700 dark:text-gray-300">
                                        {{ __('Grade') }} {{ $grade }}
                                    </span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $count }}
                                    </span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        ({{ number_format($percentage, 1) }}%)
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    @endif
</div>
