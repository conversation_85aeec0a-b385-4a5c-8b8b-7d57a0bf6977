@php
    $blurColor = match (true) {
        ($performanceMetrics['overall_performance'] ?? 0) >= 85 => 'from-green-500 dark:from-green-700',
        ($performanceMetrics['overall_performance'] ?? 0) >= 60 => 'from-yellow-400 dark:from-yellow-600',
        default => 'from-red-500 dark:from-red-800',
    };
@endphp

<div class="group relative flex flex-col overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-transform duration-300 hover:scale-105 dark:border-gray-700 dark:bg-gray-800">
    {{-- Gradient background blur --}}
    <div class="{{ $blurColor }} absolute inset-0 aspect-video -translate-y-1/2 rounded-full border bg-gradient-to-b to-white opacity-20 blur-2xl duration-300 group-hover:-translate-y-1/4"></div>

    {{-- Content container --}}
    <div class="relative z-10 flex h-full flex-col justify-between">
        <div>
            @if ($isLoading)
                {{-- Loading state --}}
                <div class="flex flex-col items-center">
                    <div class="mb-4 h-20 w-20 animate-pulse rounded-xl border bg-gray-300 dark:bg-gray-700"></div>
                    <div class="mb-6 space-y-2 text-center">
                        <div class="h-4 w-32 animate-pulse rounded bg-gray-300 dark:bg-gray-700"></div>
                        <div class="h-3 w-20 animate-pulse rounded bg-gray-300 dark:bg-gray-700"></div>
                        <div class="h-3 w-24 animate-pulse rounded bg-gray-300 dark:bg-gray-700"></div>
                    </div>
                    <div class="grid w-full grid-cols-2 gap-4">
                        @for ($i = 0; $i < 4; $i++)
                            <div class="space-y-2 rounded-lg bg-gray-100 p-4 shadow dark:bg-gray-800">
                                <div class="h-3 w-16 animate-pulse rounded bg-gray-300 dark:bg-gray-700"></div>
                                <div class="h-4 w-12 animate-pulse rounded bg-gray-300 dark:bg-gray-700"></div>
                            </div>
                        @endfor
                    </div>
                </div>
            @else
                {{-- Actual Performance Overview UI --}}
                <div class="mb-6 space-y-1 text-center">
                    <h3 class="text-base font-bold text-gray-800 dark:text-white">
                        {{ __('Performance Overview') }}
                    </h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Students') }}: {{ $performanceMetrics['total_students'] ?? 0 }}
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        {{ ucfirst(__($performanceMetrics['improvement_trend'] ?? 'stable')) }} {{ __('trend') }}
                    </p>
                </div>

                <!-- Key Metrics -->
                <div class="grid w-full grid-cols-2 gap-4">
                    <div class="rounded-lg ">
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Attendance') }}</p>
                        <p class="text-lg font-semibold text-gray-800 dark:text-white">
                            {{ number_format($performanceMetrics['attendance_percentage'] ?? 0, 1) }}%
                        </p>
                    </div>

                    <div class="rounded-lg  p-4 ">
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Average Score') }}</p>
                        <p class="text-lg font-semibold text-gray-800 dark:text-white">
                            {{ number_format($performanceMetrics['exam_average'] ?? 0, 1) }}
                        </p>
                    </div>

                    <div class="rounded-lg  col-span-2">
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Overall Performance') }}</p>
                        <p class="text-lg font-semibold text-gray-800 dark:text-white">
                            {{ number_format($performanceMetrics['overall_performance'] ?? 0, 1) }}
                        </p>
                    </div>
                </div>


            @endif
        </div>
    </div>
</div>
