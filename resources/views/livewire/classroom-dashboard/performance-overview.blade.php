@php
    $blurColor = match (true) {
        ($performanceMetrics['overall_performance'] ?? 0) >= 85 => 'from-green-500 dark:from-green-700',
        ($performanceMetrics['overall_performance'] ?? 0) >= 60 => 'from-yellow-400 dark:from-yellow-600',
        default => 'from-red-500 dark:from-red-800',
    };
@endphp

<div class="group relative flex flex-col overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-transform duration-300 hover:scale-105 dark:border-gray-700 dark:bg-gray-800">
    {{-- Gradient background blur --}}
    <div class="{{ $blurColor }} absolute inset-0 aspect-video -translate-y-1/2 rounded-full border bg-gradient-to-b to-white opacity-20 blur-2xl duration-300 group-hover:-translate-y-1/4"></div>

    {{-- Content container --}}
    <div class="relative z-10 flex h-full flex-col justify-between">
        @if($isLoading)
            <div class="space-y-4">
                <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                    <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
                <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-2"></div>
                    <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
                </div>
            </div>
            <div class="flex flex-col items-center">
                <!-- Performance Icon -->
                <div class="mb-4 h-20 w-20 rounded-xl border border-[#E5D5BB] bg-gradient-to-br from-[#C4A879] to-[#B8956A] flex items-center justify-center shadow-sm">
                    <x-filament::icon
                        :icon="$this->getPerformanceIcon()"
                        class="w-10 h-10 text-white"
                    />
                </div>

                <!-- Performance Info -->
                <div class="mb-6 space-y-1 text-center">
                    <h3 class="text-base font-bold text-gray-800 dark:text-white">{{ __('Performance Overview') }}</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $performanceMetrics['total_students'] ?? 0 }} {{ __('students') }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ ucfirst(__($performanceMetrics['improvement_trend'] ?? 'stable')) }} {{ __('trend') }}</p>
                </div>

                <!-- Key Metrics -->
                <ul class="mb-4 w-full space-y-1 text-sm text-gray-700 dark:text-gray-300">
                    <li class="flex items-start justify-between gap-2 border-b border-gray-200 pb-1 dark:border-gray-700">
                        <span class="max-w-[70%] whitespace-normal break-words text-sm text-gray-800 dark:text-gray-200">
                            {{ __('Attendance Rate') }}
                        </span>
                        <span class="text-right text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($performanceMetrics['attendance_percentage'] ?? 0, 1) }}
                            <span class="text-xs text-gray-500 dark:text-gray-400">%</span>
                        </span>
                    </li>
                    <li class="flex items-start justify-between gap-2 border-b border-gray-200 pb-1 dark:border-gray-700">
                        <span class="max-w-[70%] whitespace-normal break-words text-sm text-gray-800 dark:text-gray-200">
                            {{ __('Exam Average') }}
                        </span>
                        <span class="text-right text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($performanceMetrics['exam_average'] ?? 0, 1) }}
                            <span class="text-xs text-gray-500 dark:text-gray-400">%</span>
                        </span>
                    </li>
                    @if(!empty($performanceMetrics['grade_distribution']))
                        @foreach(['A', 'B', 'C'] as $grade)
                            @if(($performanceMetrics['grade_distribution'][$grade] ?? 0) > 0)
                                <li class="flex items-start justify-between gap-2 border-b border-gray-200 pb-1 dark:border-gray-700">
                                    <span class="max-w-[70%] whitespace-normal break-words text-sm text-gray-800 dark:text-gray-200">
                                        {{ __('Grade') }} {{ $grade }}
                                    </span>
                                    <span class="text-right text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $performanceMetrics['grade_distribution'][$grade] }}
                                        <span class="text-xs text-gray-500 dark:text-gray-400">{{ __('students') }}</span>
                                    </span>
                                </li>
                            @endif
                        @endforeach
                    @endif
                </ul>
        </div>

        <!-- Total Score at the bottom -->
        <div class="mt-auto flex flex-col items-center justify-center gap-2 pt-3 text-center text-xl font-extrabold text-gray-900 dark:text-white">
            {{ number_format($performanceMetrics['overall_performance'] ?? 0, 1) }}%
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ __('Overall Performance') }}
            </p>
        </div>
        @endif
    </div>
</div>
