@php
    $blurColor = 'from-blue-500 dark:from-blue-700';
@endphp

<div class="group relative flex flex-col overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-transform duration-300 hover:scale-105 dark:border-gray-700 dark:bg-gray-800">
    {{-- Gradient background blur --}}
    <div class="{{ $blurColor }} absolute inset-0 aspect-video -translate-y-1/2 rounded-full border bg-gradient-to-b to-white opacity-20 blur-2xl duration-300 group-hover:-translate-y-1/4"></div>

    {{-- Content container --}}
    <div class="relative z-10 flex h-full flex-col justify-between">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-base font-bold text-gray-800 dark:text-white">
                {{ __('Latest Exam Results') }}
            </h3>
            @if($isLoading)
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-[#C4A879]"></div>
            @else
                <x-filament::icon icon="heroicon-o-document-text" class="w-5 h-5 text-blue-600" />
            @endif
        </div>

        @if($isLoading)
            <div class="space-y-4">
                <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                </div>
            </div>
        @elseif(!$examResults)
            <div class="text-center py-8">
                <x-filament::icon icon="heroicon-o-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-500 dark:text-gray-400 mb-2">
                    {{ __('No exams conducted yet.') }}
                </p>
                <p class="text-sm text-gray-400 dark:text-gray-500">
                    {{ __('Exam results will appear here once students complete their first exam.') }}
                </p>
            </div>
        @else
            <div class="flex flex-col items-center">
                <!-- Exam Icon -->
                <div class="mb-4 h-20 w-20 rounded-xl border border-[#E5D5BB] bg-gradient-to-br from-[#C4A879] to-[#B8956A] flex items-center justify-center shadow-sm">
                    <x-filament::icon icon="heroicon-o-document-text" class="w-10 h-10 text-white" />
                </div>

                <!-- Exam Info -->
                <div class="mb-6 space-y-1 text-center">
                    <h3 class="text-base font-bold text-gray-800 dark:text-white">{{ $examResults['exam']->name ?? __('Latest Exam') }}</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $examResults['statistics']['total_students'] ?? 0 }} {{ __('students participated') }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ number_format($examResults['statistics']['pass_rate'] ?? 0, 1) }}% {{ __('pass rate') }}</p>
                </div>

                <!-- Exam Statistics -->
                <ul class="mb-4 w-full space-y-1 text-sm text-gray-700 dark:text-gray-300">
                    <li class="flex items-start justify-between gap-2 border-b border-gray-200 pb-1 dark:border-gray-700">
                        <span class="max-w-[70%] whitespace-normal break-words text-sm text-gray-800 dark:text-gray-200">
                            {{ __('Average Score') }}
                        </span>
                        <span class="text-right text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($examResults['statistics']['average_score'] ?? 0, 1) }}
                            <span class="text-xs text-gray-500 dark:text-gray-400">%</span>
                        </span>
                    </li>
                    <li class="flex items-start justify-between gap-2 border-b border-gray-200 pb-1 dark:border-gray-700">
                        <span class="max-w-[70%] whitespace-normal break-words text-sm text-gray-800 dark:text-gray-200">
                            {{ __('Highest Score') }}
                        </span>
                        <span class="text-right text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($examResults['statistics']['highest_score'] ?? 0, 1) }}
                            <span class="text-xs text-gray-500 dark:text-gray-400">%</span>
                        </span>
                    </li>
                    <li class="flex items-start justify-between gap-2 border-b border-gray-200 pb-1 dark:border-gray-700">
                        <span class="max-w-[70%] whitespace-normal break-words text-sm text-gray-800 dark:text-gray-200">
                            {{ __('Pass Rate') }}
                        </span>
                        <span class="text-right text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($examResults['statistics']['pass_rate'] ?? 0, 1) }}
                            <span class="text-xs text-gray-500 dark:text-gray-400">%</span>
                        </span>
                    </li>
                </ul>
        </div>

        <!-- Total Score at the bottom -->
        <div class="mt-auto flex flex-col items-center justify-center gap-2 pt-3 text-center text-xl font-extrabold text-gray-900 dark:text-white">
            {{ number_format($examResults['statistics']['average_score'] ?? 0, 1) }}%
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ __('Average Score') }}
            </p>
        </div>
        @endif
    </div>
</div>
