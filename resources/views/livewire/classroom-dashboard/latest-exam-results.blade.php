<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ __('Latest Exam Results') }}
        </h3>
        @if($isLoading)
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div>
        @else
            <x-filament::icon icon="heroicon-o-document-text" class="w-5 h-5 text-blue-600" />
        @endif
    </div>

    @if($isLoading)
        <div class="space-y-4">
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
                <div class="grid grid-cols-2 gap-4">
                    <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
            </div>
        </div>
    @elseif(!$examResults)
        <div class="text-center py-8">
            <x-filament::icon icon="heroicon-o-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400 mb-2">
                {{ __('No exams conducted yet.') }}
            </p>
            <p class="text-sm text-gray-400 dark:text-gray-500">
                {{ __('Exam results will appear here once students complete their first exam.') }}
            </p>
        </div>
    @else
        <div class="space-y-6">
            <!-- Exam Header -->
            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ $examResults['exam']->name }}
                </h4>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {{ $examResults['statistics']['total_students'] }} {{ __('students participated') }}
                </p>
            </div>

            <!-- Key Statistics -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Average Score -->
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">
                            {{ __('Average') }}
                        </span>
                        <x-filament::icon icon="heroicon-o-chart-bar" class="w-4 h-4 text-blue-600" />
                    </div>
                    <div class="text-2xl font-bold {{ $this->getScoreColor($examResults['statistics']['average_score']) }}">
                        {{ number_format($examResults['statistics']['average_score'], 1) }}%
                    </div>
                </div>

                <!-- Pass Rate -->
                <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-green-700 dark:text-green-300">
                            {{ __('Pass Rate') }}
                        </span>
                        <x-filament::icon icon="heroicon-o-check-circle" class="w-4 h-4 text-green-600" />
                    </div>
                    <div class="text-2xl font-bold {{ $this->getPassRateColor($examResults['statistics']['pass_rate']) }}">
                        {{ number_format($examResults['statistics']['pass_rate'], 1) }}%
                    </div>
                </div>

                <!-- Highest Score -->
                <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-yellow-700 dark:text-yellow-300">
                            {{ __('Highest') }}
                        </span>
                        <x-filament::icon icon="heroicon-o-trophy" class="w-4 h-4 text-yellow-600" />
                    </div>
                    <div class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                        {{ number_format($examResults['statistics']['highest_score'], 1) }}%
                    </div>
                </div>

                <!-- Lowest Score -->
                <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-red-700 dark:text-red-300">
                            {{ __('Lowest') }}
                        </span>
                        <x-filament::icon icon="heroicon-o-arrow-trending-down" class="w-4 h-4 text-red-600" />
                    </div>
                    <div class="text-2xl font-bold text-red-900 dark:text-red-100">
                        {{ number_format($examResults['statistics']['lowest_score'], 1) }}%
                    </div>
                </div>
            </div>

            <!-- Recent Results -->
            @if(!empty($examResults['results']))
                <div>
                    <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
                        {{ __('Recent Submissions') }}
                    </h5>
                    <div class="space-y-2 max-h-64 overflow-y-auto">
                        @foreach(array_slice($examResults['results'], 0, 10) as $result)
                            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-semibold text-sm">
                                        {{ substr($result['student_name'], 0, 1) }}
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $result['student_name'] }}
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ $this->formatDate($result['completed_at']) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-semibold {{ $this->getScoreColor($result['percentage']) }}">
                                        {{ number_format($result['percentage'], 1) }}%
                                    </span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $this->getGradeColor($result['grade']) }}">
                                        {{ $result['grade'] }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- View Full Results Link -->
            <div class="text-center pt-4 border-t border-gray-200 dark:border-gray-700">
                <a
                    href="{{ route('filament.admin.resources.classrooms.report', $classroom) }}"
                    class="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
                >
                    {{ __('View detailed results') }}
                    <x-filament::icon icon="heroicon-o-arrow-right" class="w-4 h-4 ml-1" />
                </a>
            </div>
        </div>
    @endif
</div>
