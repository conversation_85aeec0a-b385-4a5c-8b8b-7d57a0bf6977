@php
    $blurColor = 'from-green-500 dark:from-green-700';
@endphp

<div class="group relative flex flex-col overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-transform duration-300 hover:scale-105 dark:border-gray-700 dark:bg-gray-800">
    {{-- Gradient background blur --}}
    <div class="{{ $blurColor }} absolute inset-0 aspect-video -translate-y-1/2 rounded-full border bg-gradient-to-b to-white opacity-20 blur-2xl duration-300 group-hover:-translate-y-1/4"></div>

    {{-- Content container --}}
    <div class="relative z-10 flex h-full flex-col justify-between">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-base font-bold text-gray-800 dark:text-white">
                {{ __('Attendance Trends') }}
            </h3>
            @if($isLoading)
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-[#C4A879]"></div>
            @else
                <x-filament::icon
                    :icon="$this->getTrendIcon()"
                    class="w-5 h-5 {{ $this->getTrendColor() }}"
                />
            @endif
        </div>

    @if($isLoading)
        <div class="space-y-4">
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
                <div class="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
        </div>
    @elseif(empty($attendanceTrends))
        <div class="text-center py-8">
            <x-filament::icon icon="heroicon-o-calendar-days" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400 mb-2">
                {{ __('No attendance data available.') }}
            </p>
            <p class="text-sm text-gray-400 dark:text-gray-500">
                {{ __('Attendance trends will appear here once students start attending classes.') }}
            </p>
        </div>
    @else
        <div class="space-y-6">
            <!-- Summary Stats -->
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold {{ $this->getTrendColor() }}">
                        {{ number_format($this->getAverageAttendance(), 1) }}%
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Average') }}
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">
                        {{ $days }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Days') }}
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold {{ $this->getTrendColor() }}">
                        <x-filament::icon
                            :icon="$this->getTrendIcon()"
                            class="w-6 h-6 inline"
                        />
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ ucfirst(__($this->getTrendDirection())) }}
                    </div>
                </div>
            </div>

            <!-- Attendance Chart -->
            <div class="mb-4 w-full">
                <h4 class="text-sm font-medium text-gray-800 dark:text-gray-200 mb-3">{{ __('Last 7 Days') }}</h4>

                <!-- Simple Bar Chart -->
                <div class="space-y-2">
                    @foreach(array_slice($attendanceTrends, -7) as $trend)
                        @php
                            $attendanceColor = match (true) {
                                $trend['attendance_percentage'] >= 85 => 'bg-green-500',
                                $trend['attendance_percentage'] >= 70 => 'bg-yellow-500',
                                default => 'bg-red-500',
                            };
                            $textColor = match (true) {
                                $trend['attendance_percentage'] >= 85 => 'text-green-600',
                                $trend['attendance_percentage'] >= 70 => 'text-yellow-600',
                                default => 'text-red-600',
                            };
                        @endphp

                        <div class="flex items-center gap-3">
                            <div class="w-12 text-xs text-gray-600 dark:text-gray-400">
                                {{ $trend['formatted_date'] }}
                            </div>
                            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 relative">
                                <div
                                    class="{{ $attendanceColor }} h-2 rounded-full transition-all duration-300"
                                    style="width: {{ $trend['attendance_percentage'] }}%"
                                ></div>
                            </div>
                            <div class="w-12 text-xs font-medium {{ $textColor }} text-right">
                                {{ number_format($trend['attendance_percentage'], 0) }}%
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Legend -->
                <div class="flex items-center justify-center space-x-4 mt-3 text-xs">
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-green-500 rounded"></div>
                        <span class="text-gray-600 dark:text-gray-400">{{ __('Excellent') }} (85%+)</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-yellow-500 rounded"></div>
                        <span class="text-gray-600 dark:text-gray-400">{{ __('Good') }} (70-84%)</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-red-500 rounded"></div>
                        <span class="text-gray-600 dark:text-gray-400">{{ __('Needs Attention') }} (<70%)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Score at the bottom -->
        <div class="mt-auto flex flex-col items-center justify-center gap-2 pt-3 text-center text-xl font-extrabold text-gray-900 dark:text-white">
            {{ number_format($this->getAverageAttendance(), 1) }}%
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ __('Average Attendance') }}
            </p>
        </div>
        @endif
    </div>
</div>
