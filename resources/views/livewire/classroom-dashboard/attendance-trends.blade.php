<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ __('Attendance Trends') }}
        </h3>
        @if($isLoading)
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div>
        @else
            <x-filament::icon
                :icon="$this->getTrendIcon()"
                class="w-5 h-5 {{ $this->getTrendColor() }}"
            />
        @endif
    </div>

    @if($isLoading)
        <div class="space-y-4">
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
                <div class="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
        </div>
    @elseif(empty($attendanceTrends))
        <div class="text-center py-8">
            <x-filament::icon icon="heroicon-o-calendar-days" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400 mb-2">
                {{ __('No attendance data available.') }}
            </p>
            <p class="text-sm text-gray-400 dark:text-gray-500">
                {{ __('Attendance trends will appear here once students start attending classes.') }}
            </p>
        </div>
    @else
        <div class="space-y-6">
            <!-- Summary Stats -->
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold {{ $this->getTrendColor() }}">
                        {{ number_format($this->getAverageAttendance(), 1) }}%
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Average') }}
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">
                        {{ $days }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Days') }}
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold {{ $this->getTrendColor() }}">
                        <x-filament::icon
                            :icon="$this->getTrendIcon()"
                            class="w-6 h-6 inline"
                        />
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ ucfirst(__($this->getTrendDirection())) }}
                    </div>
                </div>
            </div>

            <!-- Chart -->
            <div class="relative">
                <div class="flex items-end justify-between h-32 space-x-1">
                    @foreach($attendanceTrends as $trend)
                        @php
                            $height = ($trend['attendance_percentage'] / $this->getMaxHeight()) * 100;
                            $height = max($height, 5); // Minimum height for visibility
                        @endphp
                        <div class="flex-1 flex flex-col items-center group">
                            <div
                                class="w-full {{ $this->getAttendanceColor($trend['attendance_percentage']) }} rounded-t transition-all duration-300 hover:opacity-80 relative"
                                style="height: {{ $height }}%"
                                title="{{ $trend['formatted_date'] }}: {{ number_format($trend['attendance_percentage'], 1) }}%"
                            >
                                <!-- Tooltip -->
                                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                                    {{ $trend['formatted_date'] }}<br>
                                    {{ number_format($trend['attendance_percentage'], 1) }}%<br>
                                    {{ $trend['present_count'] }}/{{ $trend['total_students'] }} {{ __('present') }}
                                </div>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-2 transform -rotate-45 origin-top-left">
                                {{ substr($trend['formatted_date'], 0, 5) }}
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Y-axis labels -->
                <div class="absolute left-0 top-0 h-32 flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400 -ml-8">
                    <span>{{ number_format($this->getMaxHeight(), 0) }}%</span>
                    <span>{{ number_format($this->getMaxHeight() * 0.75, 0) }}%</span>
                    <span>{{ number_format($this->getMaxHeight() * 0.5, 0) }}%</span>
                    <span>{{ number_format($this->getMaxHeight() * 0.25, 0) }}%</span>
                    <span>0%</span>
                </div>
            </div>

            <!-- Legend -->
            <div class="flex items-center justify-center space-x-6 text-sm">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded"></div>
                    <span class="text-gray-600 dark:text-gray-400">{{ __('Excellent (85%+)') }}</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-yellow-500 rounded"></div>
                    <span class="text-gray-600 dark:text-gray-400">{{ __('Good (70-84%)') }}</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-red-500 rounded"></div>
                    <span class="text-gray-600 dark:text-gray-400">{{ __('Needs Attention (<70%)') }}</span>
                </div>
            </div>

            <!-- Insights -->
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    {{ __('Insights') }}
                </h4>
                <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    @php
                        $avgAttendance = $this->getAverageAttendance();
                        $trend = $this->getTrendDirection();
                    @endphp

                    @if($avgAttendance >= 85)
                        <p>✅ {{ __('Excellent attendance rate! Keep up the great work.') }}</p>
                    @elseif($avgAttendance >= 70)
                        <p>⚠️ {{ __('Good attendance, but there\'s room for improvement.') }}</p>
                    @else
                        <p>🚨 {{ __('Attendance needs attention. Consider intervention strategies.') }}</p>
                    @endif

                    @if($trend === 'improving')
                        <p>📈 {{ __('Attendance is improving over time.') }}</p>
                    @elseif($trend === 'declining')
                        <p>📉 {{ __('Attendance is declining. Early intervention recommended.') }}</p>
                    @else
                        <p>➡️ {{ __('Attendance remains stable.') }}</p>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="flex justify-center space-x-4">
                <a
                    href="{{ route('filament.admin.resources.classrooms.attendance', $classroom) }}"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                    <x-filament::icon icon="heroicon-o-calendar-days" class="w-4 h-4 mr-2" />
                    {{ __('Mark Attendance') }}
                </a>
                <a
                    href="{{ route('filament.admin.resources.classrooms.report', $classroom) }}"
                    class="inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
                >
                    <x-filament::icon icon="heroicon-o-chart-bar" class="w-4 h-4 mr-2" />
                    {{ __('View Report') }}
                </a>
            </div>
        </div>
    @endif
</div>
