<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ __('Top Students') }}
        </h3>
        @if($isLoading)
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div>
        @else
            <x-filament::icon icon="heroicon-o-trophy" class="w-5 h-5 text-yellow-600" />
        @endif
    </div>

    @if($isLoading)
        <div class="space-y-4">
            @for($i = 0; $i < 3; $i++)
                <div class="animate-pulse flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                    <div class="flex-1">
                        <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                        <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                    </div>
                    <div class="w-16 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
            @endfor
        </div>
    @elseif(empty($topStudents))
        <div class="text-center py-8">
            <x-filament::icon icon="heroicon-o-user-group" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">
                {{ __('No student data available yet.') }}
            </p>
        </div>
    @else
        <div class="space-y-4">
            @foreach($topStudents as $index => $studentData)
                @php
                    $rank = $index + 1;
                    $student = $studentData['student'];
                    $performance = $studentData['overall_performance'];
                    $attendancePercentage = $studentData['attendance_percentage'];
                    $examAverage = $studentData['exam_average'];
                    $totalExams = $studentData['total_exams'];
                @endphp

                <div class="flex items-center space-x-4 p-4 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                    <!-- Rank Badge -->
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 rounded-full {{ $this->getRankColor($rank) }} flex items-center justify-center font-bold text-sm">
                            {{ $this->getRankIcon($rank) }}
                        </div>
                    </div>

                    <!-- Student Avatar -->
                    <div class="flex-shrink-0">
                        @if($student->hasMedia('avatar'))
                            <img
                                src="{{ $student->getFirstMediaUrl('avatar') }}"
                                alt="{{ $student->name }}"
                                class="w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
                            >
                        @else
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-semibold text-lg">
                                {{ substr($student->name, 0, 1) }}
                            </div>
                        @endif
                    </div>

                    <!-- Student Info -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white truncate">
                                {{ $student->name }}
                            </h4>
                            @if($student->registration_number)
                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                    #{{ $student->registration_number }}
                                </span>
                            @endif
                        </div>

                        <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                            <div class="flex items-center space-x-1">
                                <x-filament::icon icon="heroicon-o-calendar-days" class="w-3 h-3" />
                                <span>{{ number_format($attendancePercentage, 1) }}% {{ __('attendance') }}</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <x-filament::icon icon="heroicon-o-academic-cap" class="w-3 h-3" />
                                <span>{{ number_format($examAverage, 1) }}% {{ __('avg') }}</span>
                            </div>
                            @if($totalExams > 0)
                                <div class="flex items-center space-x-1">
                                    <x-filament::icon icon="heroicon-o-document-text" class="w-3 h-3" />
                                    <span>{{ $totalExams }} {{ __('exams') }}</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Performance Score -->
                    <div class="flex-shrink-0">
                        <div class="text-right">
                            <div class="text-lg font-bold {{ $this->getPerformanceColor($performance) }}">
                                {{ number_format($performance, 1) }}%
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                {{ __('overall') }}
                            </div>
                        </div>
                    </div>

                    <!-- Performance Badge -->
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $this->getPerformanceBadgeColor($performance) }}">
                            @if($performance >= 85)
                                {{ __('Excellent') }}
                            @elseif($performance >= 70)
                                {{ __('Good') }}
                            @else
                                {{ __('Needs Improvement') }}
                            @endif
                        </span>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- View All Link -->
        @if(count($topStudents) >= $limit)
            <div class="mt-6 text-center">
                <a
                    href="{{ route('filament.admin.resources.classrooms.students', $classroom) }}"
                    class="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
                >
                    {{ __('View all students') }}
                    <x-filament::icon icon="heroicon-o-arrow-right" class="w-4 h-4 ml-1" />
                </a>
            </div>
        @endif
    @endif
</div>
