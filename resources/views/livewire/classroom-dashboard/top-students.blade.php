@php
    $blurColor = 'from-yellow-400 dark:from-yellow-600';
@endphp

<div class="group relative flex flex-col overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-transform duration-300 hover:scale-105 dark:border-gray-700 dark:bg-gray-800">
    {{-- Gradient background blur --}}
    <div class="{{ $blurColor }} absolute inset-0 aspect-video -translate-y-1/2 rounded-full border bg-gradient-to-b to-white opacity-20 blur-2xl duration-300 group-hover:-translate-y-1/4"></div>

    {{-- Content container --}}
    <div class="relative z-10 flex h-full flex-col justify-between">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-base font-bold text-gray-800 dark:text-white">
                {{ __('Top Students') }}
            </h3>
            @if($isLoading)
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-[#C4A879]"></div>
            @else
                <x-filament::icon icon="heroicon-o-trophy" class="w-5 h-5 text-yellow-600" />
            @endif
        </div>

        @if($isLoading)
            <div class="space-y-4">
                @for($i = 0; $i < 3; $i++)
                    <div class="animate-pulse flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                        <div class="flex-1">
                            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                        </div>
                        <div class="w-16 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                @endfor
            </div>
        @elseif(empty($topStudents))
            <div class="text-center py-8">
                <x-filament::icon icon="heroicon-o-user-group" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-500 dark:text-gray-400">
                    {{ __('No student data available yet.') }}
                </p>
            </div>
            <div class="flex flex-col items-center">
                <!-- Top Students List -->
                <ul class="mb-4 w-full space-y-1 text-sm text-gray-700 dark:text-gray-300">
                    @foreach($topStudents as $index => $studentData)
                        @php
                            $rank = $index + 1;
                            $student = $studentData['student'];
                            $performance = $studentData['overall_performance'];
                            $attendancePercentage = $studentData['attendance_percentage'];
                            $examAverage = $studentData['exam_average'];
                            $totalExams = $studentData['total_exams'];

                            $photo = $student->hasMedia('student_photo')
                                ? $student->getFirstMediaUrl('student_photo')
                                : 'https://ui-avatars.com/api/?name=' . urlencode($student->name) . '&color=FFFFFF&background=C4A879&bold=true';
                        @endphp

                        <li class="flex items-center justify-between gap-2 border-b border-gray-200 pb-3 mb-3 dark:border-gray-700">
                            <div class="flex items-center space-x-3 max-w-[70%]">
                                <!-- Rank Badge -->
                                <div class="w-8 h-8 rounded-full {{ $this->getRankColor($rank) }} flex items-center justify-center font-bold text-xs">
                                    {{ $this->getRankIcon($rank) }}
                                </div>

                                <!-- Student Photo -->
                                <img src="{{ $photo }}" alt="{{ $student->name }}"
                                    class="h-10 w-10 rounded-lg border border-[#E5D5BB] object-cover shadow-sm" />

                                <!-- Student Info -->
                                <div class="min-w-0 flex-1">
                                    <div class="text-sm font-medium text-gray-800 dark:text-gray-200 truncate">
                                        {{ $student->name }}
                                    </div>
                                    @if($student->registration_number)
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            #{{ $student->registration_number }}
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <div class="text-right">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ number_format($performance, 1) }}
                                    <span class="text-xs text-gray-500 dark:text-gray-400">%</span>
                                </span>
                            </div>
                        </li>
                    @endforeach
                </ul>
        </div>

        <!-- Total Score at the bottom -->
        <div class="mt-auto flex flex-col items-center justify-center gap-2 pt-3 text-center text-xl font-extrabold text-gray-900 dark:text-white">
            {{ count($topStudents) }}
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ __('Top Performers') }}
            </p>
        </div>
        @endif
    </div>
</div>
