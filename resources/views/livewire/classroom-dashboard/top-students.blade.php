@php
    /** @var \App\Livewire\ClassroomDashboard\TopStudents $this */
    $blurColor = 'from-yellow-400 dark:from-yellow-600';
@endphp

<div class="group relative flex flex-col overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition duration-300 hover:scale-105 dark:border-gray-700 dark:bg-gray-800">
    {{-- decorative blur --}}
    <div class="{{ $blurColor }} absolute inset-0 aspect-video -translate-y-1/2 rounded-full bg-gradient-to-b to-white opacity-20 blur-2xl group-hover:-translate-y-1/4"></div>

    <div class="relative z-10 flex h-full flex-col">
        {{-- header --}}
        <div class="mb-6 flex items-center justify-between">
            <h3 class="text-base font-bold text-gray-800 dark:text-white">{{ __('Top Students') }}</h3>

            @if ($isLoading)
                <div class="h-5 w-5 animate-spin rounded-full border-b-2 border-[#C4A879]"></div>
            @else
                <x-filament::icon icon="heroicon-o-trophy" class="h-5 w-5 text-yellow-600" />
            @endif
        </div>

        {{-- 1. loading skeleton --}}
        @if ($isLoading)
            <div class="space-y-4">
                @for ($i = 0; $i < 3; $i++)
                    <div class="flex items-center space-x-4 animate-pulse">
                        <div class="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                        <div class="flex-1 space-y-1">
                            <div class="h-4 w-3/4 rounded bg-gray-200 dark:bg-gray-700"></div>
                            <div class="h-3 w-1/2 rounded bg-gray-200 dark:bg-gray-700"></div>
                        </div>
                        <div class="h-8 w-16 rounded bg-gray-200 dark:bg-gray-700"></div>
                    </div>
                @endfor
            </div>

            {{-- 2. empty-state --}}
        @elseif (empty($topStudents))
            <div class="py-8 text-center">
                <x-filament::icon icon="heroicon-o-user-group" class="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <p class="text-gray-500 dark:text-gray-400">{{ __('No student data available yet.') }}</p>
            </div>

            {{-- 3. list --}}
        @else
            <ul class="mb-6 space-y-1 text-sm text-gray-700 dark:text-gray-300">
                @foreach ($topStudents as $index => $data)
                    @php
                        $rank          = $index + 1;
                        $student       = $data['student'];
                        $performance   = $data['overall_performance'];
                        $photo         = $student->hasMedia('student_photo')
                                            ? $student->getFirstMediaUrl('student_photo')
                                            : 'https://ui-avatars.com/api/?name=' . urlencode($student->name) . '&color=FFFFFF&background=C4A879&bold=true';
                    @endphp

                    <li class="mb-3 flex items-center justify-between gap-2 border-b border-gray-200 pb-3 dark:border-gray-700">
                        {{-- left --}}
                        <div class="flex max-w-[70%] items-center space-x-3">
                            <div class="flex h-8 w-8 items-center justify-center rounded-full {{ $this->getRankColor($rank) }} text-xs font-bold">
                                {{ $this->getRankIcon($rank) }}
                            </div>

                            <img src="{{ $photo }}" alt="{{ $student->name }}" class="h-10 w-10 rounded-lg border border-[#E5D5BB] object-cover shadow-sm" />

                            <div class="min-w-0 flex-1">
                                <p class="truncate text-sm font-medium text-gray-800 dark:text-gray-200">{{ $student->name }}</p>
                                @if ($student->registration_number)
                                    <p class="text-xs text-gray-500 dark:text-gray-400">#{{ $student->registration_number }}</p>
                                @endif
                            </div>
                        </div>

                        {{-- right --}}
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($performance, 1) }}<span class="text-xs text-gray-500 dark:text-gray-400">%</span>
                        </span>
                    </li>
                @endforeach
            </ul>

            {{-- footer --}}
            <div class="mt-auto flex flex-col items-center gap-2 pt-3 text-center">
                <span class="text-xl font-extrabold text-gray-900 dark:text-white">{{ count($topStudents) }}</span>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Top Performers') }}</p>
            </div>
        @endif
    </div>
</div>
